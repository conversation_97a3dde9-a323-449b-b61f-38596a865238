* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 100%;
    margin: 0;
    padding: 10px;
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    padding-bottom: 400px; /* 为底部结果面板预留空间 */
}

header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
}

header h1 {
    font-size: 1.8rem;
    font-weight: 300;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 10px;
}

#status {
    padding: 5px 10px;
    border-radius: 5px;
    background: rgba(255, 255, 255, 0.2);
    font-size: 0.9rem;
}

#status.connected {
    background: #4CAF50;
}

#status.disconnected {
    background: #f44336;
}

button {
    background: #4CAF50;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    transition: background 0.3s;
}

button:hover {
    background: #45a049;
}

button:disabled {
    background: #ccc;
    cursor: not-allowed;
}

/* 主布局 - 左右分栏 */
.main-layout {
    display: flex;
    gap: 15px;
    flex: 1;
    overflow: hidden;
    margin-bottom: 10px; /* 与底部结果面板保持间距 */
}

/* 左侧导航 */
.left-nav {
    width: 220px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;
    overflow-y: auto;
}

.nav-header {
    padding: 20px 15px 10px;
    border-bottom: 1px solid #e0e0e0;
}

.nav-header h3 {
    margin: 0;
    color: #333;
    font-size: 1.1rem;
    font-weight: 500;
}

.nav-menu {
    list-style: none;
    padding: 10px 0;
    margin: 0;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.nav-item:hover {
    background: #f5f5f5;
}

.nav-item.active {
    background: #e3f2fd;
    border-left-color: #2196F3;
    color: #2196F3;
}

.nav-icon {
    font-size: 1.2rem;
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

.nav-text {
    font-size: 0.95rem;
    font-weight: 500;
}

/* 右侧内容区域 */
.right-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.content-view {
    display: none;
    flex: 1;
    overflow: hidden;
}

.content-view.active {
    display: flex;
    flex-direction: column;
}

.main-content {
    display: flex;
    flex-direction: column;
    gap: 10px;
    flex: 1;
    overflow: hidden;
}

.top-panel {
    flex-shrink: 0;
}

.middle-panel {
    display: flex;
    gap: 15px;
    flex: 1;
    overflow: hidden;
}

.results-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.items-panel-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}



.mode-selector {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
    flex-shrink: 0;
}

.mode-btn {
    flex: 1;
    padding: 15px;
    font-size: 1.1rem;
    background: #e0e0e0;
    color: #333;
}

.mode-btn.active {
    background: #2196F3;
    color: white;
}

.mode-panel {
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    flex: 1;
    overflow-y: auto;
}

.hidden {
    display: none;
}



/* 字段组基础样式 - 支持动画过渡 */
.form-group {
    margin-bottom: 15px;
    opacity: 1;
    max-height: 200px; /* 设置足够的最大高度以支持动画 */
    overflow: hidden;
    transform: translateY(0);
    
    /* 平滑过渡动画 - 支持多个属性同时变化 */
    transition: 
        opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1),
        max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1),
        margin 0.3s cubic-bezier(0.4, 0, 0.2, 1),
        padding 0.3s cubic-bezier(0.4, 0, 0.2, 1),
        transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    
    /* 确保动画性能优化 */
    will-change: opacity, max-height, transform;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
}

/* 隐藏状态 - 完全不占用空间 */
.form-group.hidden {
    opacity: 0;
    max-height: 0;
    margin: 0;
    padding: 0;
    transform: translateY(-10px);
    pointer-events: none; /* 防止隐藏元素接收事件 */
}

/* 字段显示动画 - 从隐藏到显示的过渡 */
.form-group.field-showing {
    animation: fieldSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* 字段隐藏动画 - 从显示到隐藏的过渡 */
.form-group.field-hiding {
    animation: fieldSlideOut 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* 字段显示关键帧动画 */
@keyframes fieldSlideIn {
    0% {
        opacity: 0;
        max-height: 0;
        margin: 0;
        padding: 0;
        transform: translateY(-10px);
    }
    50% {
        opacity: 0.5;
        transform: translateY(-5px);
    }
    100% {
        opacity: 1;
        max-height: 200px;
        margin-bottom: 15px;
        transform: translateY(0);
    }
}

/* 字段隐藏关键帧动画 */
@keyframes fieldSlideOut {
    0% {
        opacity: 1;
        max-height: 200px;
        margin-bottom: 15px;
        transform: translateY(0);
    }
    50% {
        opacity: 0.5;
        transform: translateY(-5px);
    }
    100% {
        opacity: 0;
        max-height: 0;
        margin: 0;
        padding: 0;
        transform: translateY(-10px);
    }
}

/* 字段标签样式 - 支持动态更新动画 */
.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    
    /* 标签文本变化动画 */
    transition: 
        color 0.2s cubic-bezier(0.4, 0, 0.2, 1),
        transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    
    /* 性能优化 */
    will-change: color, transform;
}

/* 标签更新时的高亮效果 */
.form-group label.label-updating {
    color: #2196F3;
    transform: scale(1.02);
}

/* 标签更新动画 */
.form-group label.label-changed {
    animation: labelPulse 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes labelPulse {
    0% {
        color: inherit;
        transform: scale(1);
    }
    50% {
        color: #2196F3;
        transform: scale(1.05);
    }
    100% {
        color: inherit;
        transform: scale(1);
    }
}

/* 表单输入元素样式 - 支持动态变化动画 */
.form-group input,
.form-group select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    
    /* 输入框状态变化动画 */
    transition: 
        border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
        box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1),
        opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1),
        transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    
    /* 性能优化 */
    will-change: border-color, box-shadow, opacity, transform;
}

/* 输入框获得焦点时的动画 */
.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #2196F3;
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
    transform: translateY(-1px);
}

/* 字段值更新时的反馈动画 */
.form-group input.field-updated,
.form-group select.field-updated {
    border-color: #4CAF50;
    box-shadow: 0 0 5px rgba(76, 175, 80, 0.3);
    animation: fieldValueUpdate 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fieldValueUpdate {
    0% {
        border-color: #4CAF50;
        box-shadow: 0 0 5px rgba(76, 175, 80, 0.3);
        transform: scale(1);
    }
    50% {
        border-color: #4CAF50;
        box-shadow: 0 0 8px rgba(76, 175, 80, 0.5);
        transform: scale(1.02);
    }
    100% {
        border-color: #ddd;
        box-shadow: none;
        transform: scale(1);
    }
}

/* 字段类型转换动画（如input转select） */
.form-group .field-transitioning {
    opacity: 0;
    transform: scale(0.95);
}

.form-group .field-transition-in {
    animation: fieldTransitionIn 0.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes fieldTransitionIn {
    0% {
        opacity: 0;
        transform: scale(0.95);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
    margin-bottom: 10px;
}

.game-form {
    background: #f9f9f9;
    padding: 15px;
    border-radius: 8px;
}

.game-form .form-row:last-of-type {
    margin-bottom: 10px;
}

.admin-form {
    background: #f9f9f9;
    padding: 15px;
    border-radius: 8px;
}

.admin-form .form-row:last-of-type {
    margin-bottom: 10px;
}

.results {
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.result-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.result-controls label {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.9rem;
}

.output-area {
    background: #1e1e1e;
    color: #00ff00;
    padding: 10px;
    border-radius: 5px;
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    flex: 1;
    overflow-y: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
}

/* 日志颜色样式 */
.log-error {
    color: #ff4444 !important;
}

.log-warning {
    color: #ffaa00 !important;
}

.log-info {
    color: #00aaff !important;
}

.log-success {
    color: #00ff88 !important;
}

.log-default {
    color: #00ff00 !important;
}

.items-panel {
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.table-container {
    overflow: auto;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    flex: 1;
}

.items-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
    background: #1a1a1a;
    color: #ffffff;
    table-layout: fixed;
}

.items-table col:nth-child(1) {
    width: 40%;
}

.items-table col:nth-child(2) {
    width: 20%;
}

.items-table col:nth-child(3) {
    width: 20%;
}

.items-table col:nth-child(4) {
    width: 20%;
}

.items-table thead {
    background: #2d2d2d;
}

.items-table th {
    padding: 12px 15px;
    font-weight: 600;
    border-bottom: 2px solid #404040;
    color: #ffffff;
}

.items-table th:first-child {
    text-align: left;
}

.items-table th:not(:first-child) {
    text-align: right;
}

.items-table td {
    padding: 10px 15px;
    border-bottom: 1px solid #404040;
}

.items-table tbody tr:hover {
    background: #2a2a2a;
}

.items-table tbody tr:nth-child(even) {
    background: #1f1f1f;
}

.items-table tbody tr:nth-child(even):hover {
    background: #2a2a2a;
}

.items-table .no-data {
    text-align: center;
    color: #888;
    font-style: italic;
}

.items-table .item-name {
    font-weight: 500;
    color: #4CAF50;
    text-align: left;
}

.items-table .item-value {
    color: #FFC107;
    text-align: right;
    font-family: 'Courier New', monospace;
}

.items-table .item-count {
    color: #2196F3;
    text-align: right;
    font-family: 'Courier New', monospace;
}

.items-table .item-total {
    color: #FF5722;
    text-align: right;
    font-weight: 600;
    font-family: 'Courier New', monospace;
}



.error {
    color: #f44336;
}

.success {
    color: #4CAF50;
}

.warning {
    color: #ff9800;
}

.info {
    color: #2196F3;
}

/* 日志查看器样式 */
.log-viewer-container {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.log-viewer-container h2 {
    color: #333;
    margin-bottom: 20px;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.placeholder-text {
    color: #666;
    font-size: 1rem;
    margin: 0;
}

/* 日志控制面板 */
.log-controls {
    margin-bottom: 20px;
    flex-shrink: 0;
}

.control-row {
    display: flex;
    gap: 15px;
    align-items: end;
    flex-wrap: wrap;
}

.control-row .form-group {
    margin-bottom: 0;
    min-width: 150px;
}

.control-row .form-group:last-child {
    margin-left: auto;
}

.control-row label {
    font-size: 0.9rem;
    font-weight: 500;
    color: #555;
}

.control-row input[type="checkbox"] {
    width: auto;
    margin-right: 5px;
}

/* 加载指示器 - 现代化设计 */
.loading-indicator {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 18px 24px;
    border-radius: 12px;
    text-align: center;
    font-size: 0.95rem;
    font-weight: 500;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    box-shadow: 
        0 4px 15px rgba(102, 126, 234, 0.3),
        0 2px 8px rgba(0, 0, 0, 0.1);
    animation: loadingPulse 2s ease-in-out infinite;
    position: relative;
    overflow: hidden;
}

.loading-indicator::before {
    content: '';
    width: 22px;
    height: 22px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    filter: drop-shadow(0 0 3px rgba(255, 255, 255, 0.5));
}

.loading-indicator::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer 2s infinite;
}

@keyframes loadingPulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 
            0 4px 15px rgba(102, 126, 234, 0.3),
            0 2px 8px rgba(0, 0, 0, 0.1);
    }
    50% {
        transform: scale(1.02);
        box-shadow: 
            0 6px 20px rgba(102, 126, 234, 0.4),
            0 3px 12px rgba(0, 0, 0, 0.15);
    }
}

@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

@keyframes pulse {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.7;
    }
}

/* 错误信息 */
.error-message {
    background: #ffebee;
    color: #c62828;
    padding: 10px 15px;
    border-radius: 5px;
    margin-bottom: 15px;
    border-left: 4px solid #f44336;
    font-size: 0.9rem;
}

/* 文件信息 */
.file-info {
    background: #f5f5f5;
    padding: 8px 12px;
    border-radius: 5px;
    font-size: 0.85rem;
    color: #666;
    margin-bottom: 15px;
    border-left: 4px solid #4CAF50;
}

/* 日志内容容器 */
.log-content-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    position: relative;
    min-height: 0;
    /* 确保flex子元素可以收缩 */
}

.log-content {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    background: #1e1e1e;
    color: #e0e0e0;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.85rem;
    line-height: 1.5;
    padding: 15px;
    scroll-behavior: smooth;
    /* 移动端滚动优化 */
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
    /* 确保有明确的高度限制 */
    max-height: 100%;
    min-height: 200px;
}

.log-content-text {
    white-space: pre-wrap;
    word-wrap: break-word;
    font-feature-settings: "liga" 0;
}

.no-content {
    color: #888;
    text-align: center;
    padding: 60px 20px;
    font-style: italic;
    font-size: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    margin: 20px;
    border: 2px dashed #ddd;
}

/* 自动滚动指示器 */
.auto-scroll-indicator {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background: rgba(33, 150, 243, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    display: none;
    z-index: 10;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    animation: fadeInOut 2s ease-in-out;
}

.auto-scroll-indicator.show {
    display: block;
}

@keyframes fadeInOut {

    0%,
    100% {
        opacity: 0;
    }

    50% {
        opacity: 1;
    }
}

/* 滚动到底部按钮 */
.scroll-to-bottom {
    position: absolute;
    bottom: 15px;
    right: 15px;
    background: #2196F3;
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    cursor: pointer;
    display: none;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
    transition: all 0.3s ease;
    z-index: 10;
}

.scroll-to-bottom:hover {
    background: #1976d2;
    transform: scale(1.1);
}

.scroll-to-bottom.show {
    display: flex;
}

/* 日志级别高亮样式 - 简洁清晰 */
.log-level-error {
    color: #f48fb1 !important; /* 柔和粉红色 */
}

.log-level-warning {
    color: #ffb74d !important; /* 柔和橙色 */
}

.log-level-info {
    color: #90caf9 !important; /* 柔和蓝色 */
}

.log-level-debug {
    color: #ce93d8 !important; /* 柔和紫色 */
}

.log-level-success {
    color: #a5d6a7 !important; /* 柔和绿色 */
}

/* 时间戳高亮 */
.log-timestamp {
    color: #868e96 !important;
    font-weight: normal;
}

/* JSON 格式化 */
.log-json {
    background: rgba(116, 192, 252, 0.1);
    border-left: 3px solid #74c0fc;
    padding: 8px 12px;
    margin: 4px 0;
    border-radius: 4px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    white-space: pre-wrap;
    overflow-x: auto;
}

/* 滚动条样式 */
.log-content::-webkit-scrollbar {
    width: 10px;
}

.log-content::-webkit-scrollbar-track {
    background: #2d2d2d;
    border-radius: 5px;
}

.log-content::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #555 0%, #777 100%);
    border-radius: 5px;
    border: 1px solid #444;
}

.log-content::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #777 0%, #999 100%);
}

.log-content::-webkit-scrollbar-corner {
    background: #2d2d2d;
}

/* 进度条样式 */
.progress-bar {
    width: 100%;
    height: 4px;
    background: #e0e0e0;
    border-radius: 2px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-bar-fill {
    height: 100%;
    background: linear-gradient(90deg, #2196F3, #21CBF3);
    border-radius: 2px;
    transition: width 0.3s ease;
    animation: progressShine 2s ease-in-out infinite;
}

@keyframes progressShine {
    0% {
        background-position: -100% 0;
    }

    100% {
        background-position: 100% 0;
    }
}

/* 接口9特殊字段动画支持 */
.form-group .manual-lottery-field {
    position: relative;
}

/* 宝物数量字段的条件显示动画 */
.form-group .treasure-count-field {
    transition: 
        opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1),
        max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1),
        transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.form-group .treasure-count-field.conditional-show {
    animation: conditionalFieldShow 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.form-group .treasure-count-field.conditional-hide {
    animation: conditionalFieldHide 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes conditionalFieldShow {
    0% {
        opacity: 0;
        max-height: 0;
        transform: translateX(-10px);
    }
    100% {
        opacity: 1;
        max-height: 100px;
        transform: translateX(0);
    }
}

@keyframes conditionalFieldHide {
    0% {
        opacity: 1;
        max-height: 100px;
        transform: translateX(0);
    }
    100% {
        opacity: 0;
        max-height: 0;
        transform: translateX(-10px);
    }
}

/* 动画性能优化 - 减少重绘和重排 */
.form-group,
.form-group label,
.form-group input,
.form-group select {
    /* 启用硬件加速 */
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
    
    /* 优化渲染 */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 减少动画期间的重排 */
.form-group.field-showing,
.form-group.field-hiding,
.form-group .field-transitioning,
.form-group .field-transition-in {
    /* 将元素提升到合成层 */
    will-change: transform, opacity, max-height;
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
}

/* 动画完成后清理will-change属性 */
.form-group.animation-complete {
    will-change: auto;
}

/* 预加载动画状态 - 防止首次动画卡顿 */
.form-group::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 0;
    opacity: 0;
    transition: opacity 0s;
    pointer-events: none;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .form-group.field-showing,
    .form-group.field-hiding {
        transition-duration: 0.1s; /* 减少动画时间 */
    }
    
    .form-group label.label-updating {
        color: #000;
        background-color: #ffff00;
        padding: 2px 4px;
        border-radius: 2px;
    }
}

/* 减少动画偏好设置支持 */
@media (prefers-reduced-motion: reduce) {
    .form-group,
    .form-group label,
    .form-group input,
    .form-group select {
        transition: none !important;
        animation: none !important;
    }
    
    .form-group.field-showing,
    .form-group.field-hiding {
        animation: none !important;
        transition: none !important;
    }
    
    /* 立即显示/隐藏，无动画 */
    .form-group.hidden {
        display: none;
    }
    
    .form-group:not(.hidden) {
        display: block;
        opacity: 1;
        max-height: none;
        margin-bottom: 15px;
        transform: none;
    }
}

/* 全局底部结果面板样式 */
.global-results-footer {
    position: fixed;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: calc(100% - 20px);
    max-width: 1400px;
    background: white;
    border-top: 2px solid #e0e0e0;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    height: 400px;
    overflow: hidden;
    border-radius: 8px 8px 0 0;
}

.global-results-footer .results-panel {
    height: 100%;
    padding: 0;
    margin: 0;
}

.global-results-footer .results {
    height: 100%;
    margin: 0;
    border-radius: 0;
    box-shadow: none;
    border: none;
}

.global-results-footer .output-area {
    height: calc(100% - 60px); /* 减去标题和控制按钮的高度 */
    max-height: none;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .middle-panel {
        flex-direction: column;
    }

    .results-panel,
    .items-panel-wrapper {
        flex: none;
        margin-bottom: 10px;
    }

    .container {
        height: auto;
        overflow: visible;
        padding-bottom: 420px; /* 为底部结果面板预留更多空间 */
    }

    .global-results-footer {
        height: 400px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
        height: 100vh;
        overflow: hidden;
        padding-bottom: 440px; /* 移动端为底部结果面板预留更多空间 */
    }

    header {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    header h1 {
        font-size: 1.5rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .mode-selector {
        flex-direction: column;
    }

    .middle-panel {
        flex-direction: column;
    }

    .results-panel,
    .items-panel-wrapper {
        margin-bottom: 10px;
    }

    .global-results-footer {
        height: 420px;
        left: 10px;
        right: 10px;
    }

    .global-results-footer .output-area {
        height: calc(100% - 70px); /* 移动端调整高度 */
        font-size: 0.75rem;
    }
    
    /* 移动端动画优化 */
    .form-group {
        /* 减少移动端动画时间以提升响应性 */
        transition-duration: 0.2s;
    }
    
    .form-group.field-showing,
    .form-group.field-hiding {
        animation-duration: 0.2s;
    }
    
    /* 移动端减少动画复杂度 */
    .form-group label.label-updating {
        transform: none; /* 移动端不使用缩放动画 */
    }
    
    .form-group input:focus,
    .form-group select:focus {
        transform: none; /* 移动端不使用位移动画 */
    }

    /* 移动端导航调整 */
    .main-layout {
        flex-direction: column;
        gap: 10px;
        overflow: hidden;
    }

    .left-nav {
        width: 100%;
        order: 2;
        flex-shrink: 0;
    }

    .right-content {
        order: 1;
        flex: 1;
        overflow: hidden;
    }

    .nav-menu {
        display: flex;
        padding: 5px;
        gap: 5px;
    }

    .nav-item {
        flex: 1;
        justify-content: center;
        padding: 10px 5px;
        border-radius: 5px;
        border-left: none;
        border-bottom: 3px solid transparent;
    }

    .nav-item.active {
        border-left: none;
        border-bottom-color: #2196F3;
    }

    .nav-header {
        display: none;
    }

    .nav-text {
        font-size: 0.85rem;
    }

    /* 日志查看器移动端适配 */
    .control-row {
        flex-direction: column;
        gap: 10px;
    }

    .control-row .form-group {
        min-width: auto;
        width: 100%;
    }

    .control-row .form-group:last-child {
        margin-left: 0;
    }

    .log-viewer-container {
        padding: 15px;
        height: 100%;
        overflow: hidden;
    }

    .log-viewer-container h2 {
        font-size: 1.3rem;
        margin-bottom: 15px;
    }

    .log-content-container {
        /* 移动端关键修复：设置固定高度 */
        height: calc(100vh - 300px);
        min-height: 300px;
        max-height: calc(100vh - 300px);
    }

    .log-content {
        font-size: 0.75rem;
        padding: 10px;
        line-height: 1.4;
        /* 移动端滚动关键属性 */
        -webkit-overflow-scrolling: touch;
        overscroll-behavior: contain;
        /* 确保滚动容器有明确的高度 */
        height: 100%;
        max-height: 100%;
        overflow-y: scroll !important;
        overflow-x: hidden;
    }

    .loading-indicator {
        padding: 12px 15px;
        font-size: 0.85rem;
    }

    .file-info {
        font-size: 0.8rem;
        padding: 6px 10px;
    }

    .error-message {
        font-size: 0.85rem;
        padding: 8px 12px;
    }

    .scroll-to-bottom {
        width: 35px;
        height: 35px;
        bottom: 10px;
        right: 10px;
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 5px;
        height: 100vh;
        overflow: hidden;
        padding-bottom: 460px; /* 小屏幕为底部结果面板预留更多空间 */
    }

    .nav-icon {
        margin-right: 5px;
        font-size: 1rem;
    }

    .nav-text {
        font-size: 0.8rem;
    }

    .log-viewer-container {
        padding: 10px;
        height: 100%;
        overflow: hidden;
    }

    .log-viewer-container h2 {
        font-size: 1.2rem;
        margin-bottom: 10px;
    }

    .log-content-container {
        /* 小屏幕设备的高度调整，考虑底部结果面板 */
        height: calc(100vh - 740px);
        min-height: 200px;
        max-height: calc(100vh - 740px);
    }

    .log-content {
        font-size: 0.7rem;
        padding: 8px;
        /* 小屏幕滚动优化 */
        -webkit-overflow-scrolling: touch;
        overscroll-behavior: contain;
        height: 100%;
        max-height: 100%;
        overflow-y: scroll !important;
        overflow-x: hidden;
        /* 防止内容溢出 */
        word-break: break-all;
        white-space: pre-wrap;
    }

    .control-row .form-group label {
        font-size: 0.85rem;
    }

    .control-row .form-group input,
    .control-row .form-group select {
        font-size: 0.9rem;
        padding: 8px;
    }

    .global-results-footer {
        height: 440px;
        left: 5px;
        right: 5px;
    }

    .global-results-footer .output-area {
        height: calc(100% - 80px); /* 小屏幕调整高度 */
        font-size: 0.7rem;
    }
}

/* 超大屏幕优化 */
@media (min-width: 1400px) {
    .container {
        max-width: 1400px;
        margin: 0 auto;
    }

    .global-results-footer {
        max-width: 1380px; /* 与容器宽度保持一致，减去左右padding */
    }

    .log-content {
        font-size: 0.9rem;
        line-height: 1.6;
    }

    .log-viewer-container h2 {
        font-size: 1.6rem;
    }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2),
(min-resolution: 192dpi) {
    .log-content {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
}

/* 新增样式 - 模块化重构后的样式 */

/* 按钮组样式 */
.button-group {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.button-group button {
    flex: 1;
}

/* 验证错误样式 */
.validation-errors {
    background: #ffebee;
    border: 1px solid #f44336;
    border-radius: 4px;
    padding: 10px;
    margin: 10px 0;
}

.validation-error-item {
    color: #d32f2f;
    margin: 5px 0;
}

.error-message {
    color: #d32f2f;
    font-size: 0.9rem;
    margin-top: 5px;
}

input.error, select.error {
    border-color: #f44336;
    background-color: #ffebee;
}

/* 日志查看器样式 */
.log-viewer-container {
    padding: 20px;
}

.log-controls {
    background: white;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.control-row {
    display: flex;
    gap: 15px;
    align-items: end;
    flex-wrap: wrap;
}

.control-row .form-group {
    margin-bottom: 0;
}

.log-content-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

.log-content {
    max-height: 600px;
    overflow-y: auto;
    padding: 15px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.4;
}

.log-line {
    display: flex;
    margin-bottom: 2px;
    padding: 2px 0;
}

.log-line:hover {
    background: rgba(255, 255, 255, 0.05);
}

.line-number {
    color: #666;
    margin-right: 10px;
    min-width: 50px;
    text-align: right;
    user-select: none;
    font-size: 0.8rem;
}

.line-content {
    flex: 1;
    word-break: break-word;
    color: #e0e0e0;
}



.no-data-message {
    text-align: center;
    color: #666;
    padding: 40px;
    font-style: italic;
}

/* 分页样式 */
.pagination-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: #f5f5f5;
    border-bottom: 1px solid #ddd;
}

.pagination-controls {
    display: flex;
    gap: 10px;
}

.pagination-controls button {
    padding: 5px 15px;
    font-size: 0.9rem;
}

/* 搜索高亮 */
mark {
    background-color: #ffeb3b;
    padding: 1px 2px;
    border-radius: 2px;
}

/* 加载状态 */
.loading-indicator {
    text-align: center;
    padding: 20px;
    color: #666;
}

.loading-indicator:before {
    content: "⏳ ";
}

/* 日志条目样式 */
.log-entry {
    display: flex;
    padding: 8px 12px;
    margin-bottom: 2px;
    border-radius: 4px;
    border-left: 3px solid transparent;
    transition: all 0.2s ease;
    font-size: 0.9rem;
    line-height: 1.4;
}

.log-entry:hover {
    background-color: #f8f9fa;
}

.log-time {
    color: #666;
    margin-right: 12px;
    min-width: 85px;
    font-size: 0.85rem;
    font-family: 'Courier New', monospace;
    flex-shrink: 0;
}

.log-type {
    margin-right: 12px;
    min-width: 50px;
    font-weight: 600;
    font-size: 0.8rem;
    text-align: center;
    padding: 2px 6px;
    border-radius: 3px;
    flex-shrink: 0;
}

.log-message {
    flex: 1;
    word-break: break-word;
    white-space: pre-wrap;
}

/* 成功日志样式 */
.log-success {
    border-left-color: #4caf50;
    background-color: rgba(76, 175, 80, 0.05);
}

.log-success .log-type {
    color: #2e7d32;
    background-color: rgba(76, 175, 80, 0.15);
}

/* 错误日志样式 */
.log-error {
    border-left-color: #f44336;
    background-color: rgba(244, 67, 54, 0.05);
}

.log-error .log-type {
    color: #c62828;
    background-color: rgba(244, 67, 54, 0.15);
}

/* 警告日志样式 */
.log-warning {
    border-left-color: #ff9800;
    background-color: rgba(255, 152, 0, 0.05);
}

.log-warning .log-type {
    color: #ef6c00;
    background-color: rgba(255, 152, 0, 0.15);
}

/* 信息日志样式 */
.log-info {
    border-left-color: #2196f3;
    background-color: rgba(33, 150, 243, 0.05);
}

.log-info .log-type {
    color: #1565c0;
    background-color: rgba(33, 150, 243, 0.15);
}

/* 调试日志样式 */
.log-debug {
    border-left-color: #9c27b0;
    background-color: rgba(156, 39, 176, 0.05);
    opacity: 0.8;
}

.log-debug .log-type {
    color: #7b1fa2;
    background-color: rgba(156, 39, 176, 0.15);
}

/* 系统日志样式 - 重要的系统级消息 */
.log-system {
    border-left-color: #607d8b;
    background-color: rgba(96, 125, 139, 0.08);
    font-weight: 500;
}

.log-system .log-type {
    color: #37474f;
    background-color: rgba(96, 125, 139, 0.2);
}

/* 结果日志样式 - 操作结果 */
.log-result {
    border-left-width: 4px;
    font-weight: 500;
}

/* JSON 格式化显示 */
.log-message pre {
    background: rgba(0, 0, 0, 0.05);
    padding: 8px;
    border-radius: 4px;
    margin: 4px 0;
    font-size: 0.85rem;
    overflow-x: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .control-row {
        flex-direction: column;
        align-items: stretch;
    }
    
    .button-group {
        flex-direction: column;
    }
    
    .pagination-info {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .log-line {
        flex-direction: column;
    }
    
    .line-number {
        margin-right: 0;
        margin-bottom: 2px;
        text-align: left;
    }
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { 
        transform: translateY(-10px);
        opacity: 0;
    }
    to { 
        transform: translateY(0);
        opacity: 1;
    }
}

/* 工具提示 */
[title] {
    position: relative;
    cursor: help;
}

/* 滚动条样式 */
.log-content::-webkit-scrollbar {
    width: 8px;
}

.log-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.log-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.log-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}