<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>奖池系统管理后台</title>
    <link rel="stylesheet" href="style.css?v=8">
</head>

<body>
    <div class="container">
        <header>
            <h1>奖池系统管理后台</h1>
            <div class="connection-status">
                <span id="status">未连接</span>
                <button id="connectBtn">连接</button>
            </div>
        </header>

        <div class="main-layout">
            <!-- 左侧导航 -->
            <nav class="left-nav">
                <div class="nav-header">
                    <h3>功能导航</h3>
                </div>
                <ul class="nav-menu">
                    <li class="nav-item active" data-view="api-test">
                        <span class="nav-icon">🔧</span>
                        <span class="nav-text">接口测试</span>
                    </li>
                    <li class="nav-item" data-view="log-viewer">
                        <span class="nav-icon">📋</span>
                        <span class="nav-text">日志查看</span>
                    </li>
                </ul>
            </nav>

            <!-- 右侧内容区域 -->
            <div class="right-content">
                <!-- API测试视图 -->
                <div id="api-test-view" class="content-view active">
                    <div class="mode-selector">
                        <button id="adminModeBtn" class="mode-btn">管理员模式</button>
                        <button id="gameModeBtn" class="mode-btn">游戏接口模式</button>
                    </div>

                    <div class="main-content">
                        <!-- 顶部面板 - 控制区域 -->
                        <div class="top-panel">
                            <!-- 管理员模式 -->
                            <div id="adminMode" class="mode-panel hidden">
                                <h2>管理员模式</h2>
                                <div class="admin-form">
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label>操作类型:</label>
                                            <select id="adminOperation">
                                                <option value="stats_get">获取统计 -- GET /stats/global/{mapId}</option>
                                                <option value="stats_set">设置统计 -- POST /stats/global/{mapId}/{key}
                                                </option>
                                                <option value="module_info">模块信息 -- GET /module?mode={mode}</option>
                                                <option value="cleanup">数据清理 -- POST /stats/cleanup</option>
                                            </select>
                                        </div>

                                        <!-- 地图ID参数 -->
                                        <div class="form-group" id="mapIdGroup">
                                            <label>地图ID:</label>
                                            <input type="number" id="adminMapId" placeholder="例如: 100, 1000, 10000">
                                        </div>

                                        <!-- 统计键参数 -->
                                        <div class="form-group hidden" id="statsKeyGroup">
                                            <label>键:</label>
                                            <input type="text" id="adminStatsKey" placeholder="例如: rtp">
                                        </div>

                                        <!-- 统计值参数 -->
                                        <div class="form-group hidden" id="statsValueGroup">
                                            <label>值:</label>
                                            <input type="text" id="adminStatsValue" placeholder="例如: 0.95">
                                        </div>

                                        <!-- 模块查询模式参数 -->
                                        <div class="form-group hidden" id="moduleModeGroup">
                                            <label>查询模式:</label>
                                            <select id="adminModuleMode">
                                                <option value="basic">基础信息</option>
                                                <option value="admin">管理员模式</option>
                                                <option value="detailed">详细模式</option>
                                            </select>
                                        </div>
                                    </div>
                                    <button id="adminSubmit">执行</button>
                                </div>
                            </div>

                            <!-- 游戏接口模式 -->
                            <div id="gameMode" class="mode-panel hidden">
                                <h2>游戏接口模式</h2>
                                <div class="game-form">
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label>用户ID:</label>
                                            <input type="number" id="userId" value="1001" min="1">
                                        </div>
                                        <div class="form-group">
                                            <label>接口:</label>
                                            <select id="apiType">
                                                <option value="1">普通怪 (1)</option>
                                                <option value="4">补充奖池 (4)</option>
                                                <option value="9">宝物开奖 (9)</option>
                                                <option value="11">内丹探索 (11)</option>
                                                <option value="20">仙丹掉落 (20)</option>
                                                <option value="21">仙丹开鼎 (21)</option>
                                                <option value="22">内丹探索仙丹 (22)</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label>成本:</label>
                                            <select id="costType">
                                                <option value="100">100</option>
                                                <option value="1000">1000</option>
                                                <option value="10000">10000</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label>连击:</label>
                                            <input type="number" id="repeat" value="1" min="1">
                                        </div>
                                        <div class="form-group">
                                            <label>队伍:</label>
                                            <input type="number" id="team" value="0" min="0">
                                        </div>
                                        <div class="form-group">
                                            <label>精英:</label>
                                            <input type="number" id="jy" value="0" min="0">
                                        </div>
                                    </div>
                                    <div class="button-group">
                                        <button id="sendBtn">发送</button>
                                        <button id="quickTestBtn">快速测试</button>
                                        <button id="batchTestBtn">批量测试</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 中间面板 - 道具列表（结果面板已移至全局底部） -->
                        <div class="middle-panel">
                            <!-- 道具列表 -->
                            <div class="items-panel-wrapper">
                                <div class="items-panel">
                                    <h3>道具列表</h3>
                                    <div class="table-container">
                                        <table id="itemsTable" class="items-table">
                                            <colgroup>
                                                <col style="width: 30%;">
                                                <col style="width: 15%;">
                                                <col style="width: 15%;">
                                                <col style="width: 15%;">
                                                <col style="width: 25%;">
                                            </colgroup>
                                            <thead>
                                                <tr>
                                                    <th>名称</th>
                                                    <th>单价</th>
                                                    <th>数量</th>
                                                    <th>总量</th>
                                                    <th>总值</th>
                                                </tr>
                                            </thead>
                                            <tbody id="itemsTableBody">
                                                <tr class="no-data">
                                                    <td colspan="5">暂无数据</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>


                    </div>
                </div>

                <!-- 日志查看视图 -->
                <div id="log-viewer-view" class="content-view">
                    <div class="log-viewer-container">
                        <h2>日志查看功能</h2>

                        <!-- 控制面板 -->
                        <div class="log-controls">
                            <div class="control-row">
                                <div class="form-group">
                                    <label for="logFileSelect">选择日志文件:</label>
                                    <select id="logFileSelect">
                                        <option value="">加载中...</option>
                                    </select>
                                </div>



                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="autoRefreshToggle">
                                        自动刷新
                                    </label>
                                </div>

                                <div class="form-group">
                                    <button id="logRefreshBtn" type="button">刷新</button>
                                    <button id="logClearBtn" type="button">清空</button>
                                    <button id="logDownloadBtn" type="button">下载</button>
                                </div>
                            </div>
                        </div>

                        <!-- 加载指示器 -->
                        <div id="logLoadingIndicator" class="loading-indicator hidden">
                            加载中...
                        </div>

                        <!-- 分页信息 -->
                        <div class="pagination-info">
                            <span id="logPageInfo"></span>
                            <div class="pagination-controls">
                                <button id="logPrevBtn">上一页</button>
                                <button id="logNextBtn">下一页</button>
                            </div>
                        </div>

                        <!-- 日志内容显示区域 -->
                        <div class="log-content-container">
                            <div id="logDisplay" class="log-content">
                                <div class="no-content">请选择日志文件查看内容</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 全局底部结果面板（跨视图可见） -->
    <div class="global-results-footer">
        <div class="results-panel">
            <div class="results">
                <h3>结果</h3>
                <div class="result-controls">
                    <label>
                        <input type="checkbox" id="verboseLogging">
                        详细日志
                    </label>
                    <button id="clearBtn">清空结果</button>
                </div>
                <div id="output" class="output-area"></div>
            </div>
        </div>
    </div>

    <script type="module" src="js/app.js"></script>
</body>

</html>