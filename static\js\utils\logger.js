// 日志工具类
export class Logger {
    constructor(containerId = 'output') {
        this.container = document.getElementById(containerId);
        this.maxLogs = 500; // 减少最大日志条数，提高性能
        this.verboseMode = false; // 详细模式开关
        this.logBuffer = []; // 日志缓冲区
    }

    // 设置详细模式
    setVerboseMode(enabled) {
        this.verboseMode = enabled;
        this.info(enabled ? '已启用详细日志模式' : '已关闭详细日志模式');
    }

    log(message, type = 'info', forceShow = false) {
        if (!this.container) {
            console.warn('日志容器未找到');
            return;
        }

        // 如果不是详细模式且不是强制显示，跳过debug类型的日志
        if (!this.verboseMode && type === 'debug' && !forceShow) {
            return;
        }

        const timestamp = new Date().toLocaleTimeString('zh-CN', { 
            hour12: false,
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        
        const logEntry = document.createElement('div');
        logEntry.className = `log-entry log-${type}`;
        
        // 格式化消息
        let formattedMessage = this.formatMessage(message, type);

        logEntry.innerHTML = `
            <span class="log-time">[${timestamp}]</span>
            <span class="log-type">[${this.getTypeLabel(type)}]</span>
            <span class="log-message">${formattedMessage}</span>
        `;

        // 添加到容器顶部
        this.container.insertBefore(logEntry, this.container.firstChild);

        // 限制日志数量
        this.limitLogCount();

        // 控制台输出（仅在开发模式下）
        if (this.verboseMode || type !== 'debug') {
            this.consoleLog(message, type);
        }

        // 保存到缓冲区
        this.logBuffer.unshift({
            timestamp,
            type,
            message: formattedMessage,
            raw: message
        });

        if (this.logBuffer.length > this.maxLogs) {
            this.logBuffer = this.logBuffer.slice(0, this.maxLogs);
        }
    }

    // 格式化消息
    formatMessage(message, type) {
        if (message === undefined) {
            return 'undefined';
        }
        
        if (message === null) {
            return 'null';
        }
        
        if (typeof message === 'object') {
            // 特殊处理常见的对象类型
            if (message instanceof Error) {
                return `错误: ${message.message}`;
            }
            
            // 对于复杂对象，提供更好的格式化
            try {
                return this.formatObject(message, type);
            } catch (e) {
                return String(message);
            }
        }
        
        return String(message);
    }

    // 格式化对象
    formatObject(obj, type) {
        // 对于游戏响应数据，提供更友好的显示
        if (obj.header && obj.data) {
            const { header, data } = obj;
            return `消息类型: ${header.messageType}, 用户: ${header.userId}, 数据: ${JSON.stringify(data, null, 2)}`;
        }
        
        // 对于包含items的响应，突出显示道具信息
        if (obj.data && obj.data.items && Array.isArray(obj.data.items)) {
            const itemCount = obj.data.items.length;
            const summary = itemCount > 0 ? `获得${itemCount}种道具` : '未获得道具';
            return `${summary} - ${JSON.stringify(obj, null, 2)}`;
        }
        
        // 对于错误对象，提供更清晰的显示
        if (obj.error) {
            return `操作失败: ${obj.error}`;
        }
        
        // 默认JSON格式化
        return JSON.stringify(obj, null, 2);
    }

    // 获取类型标签
    getTypeLabel(type) {
        const labels = {
            'info': '信息',
            'success': '成功', 
            'warning': '警告',
            'error': '错误',
            'debug': '调试'
        };
        return labels[type] || type.toUpperCase();
    }

    info(message, forceShow = false) {
        this.log(message, 'info', forceShow);
    }

    success(message, forceShow = false) {
        this.log(message, 'success', forceShow);
    }

    warning(message, forceShow = false) {
        this.log(message, 'warning', forceShow);
    }

    error(message, forceShow = true) {
        this.log(message, 'error', forceShow);
    }

    debug(message, forceShow = false) {
        this.log(message, 'debug', forceShow);
    }

    // 系统级别的重要信息，总是显示
    system(message) {
        this.log(message, 'info', true);
    }

    // 操作结果，总是显示
    result(message, isSuccess = true) {
        this.log(message, isSuccess ? 'success' : 'error', true);
    }

    clear() {
        if (this.container) {
            this.container.innerHTML = '';
        }
    }

    limitLogCount() {
        if (!this.container) return;

        const logs = this.container.querySelectorAll('.log-entry');
        if (logs.length > this.maxLogs) {
            // 删除多余的日志条目
            for (let i = this.maxLogs; i < logs.length; i++) {
                logs[i].remove();
            }
        }
    }

    consoleLog(message, type) {
        const consoleMethod = console[type] || console.log;
        consoleMethod(`[${type.toUpperCase()}]`, message);
    }

    // 导出日志
    exportLogs() {
        return this.logBuffer.map(log => 
            `[${log.timestamp}] [${this.getTypeLabel(log.type)}] ${log.message}`
        ).join('\n');
    }

    // 搜索日志
    searchLogs(keyword) {
        if (!keyword) return [];
        
        const lowerKeyword = keyword.toLowerCase();
        return this.logBuffer.filter(log => 
            log.message.toLowerCase().includes(lowerKeyword) ||
            log.type.toLowerCase().includes(lowerKeyword)
        );
    }

    // 获取统计信息
    getStats() {
        const stats = {
            total: this.logBuffer.length,
            info: 0,
            success: 0,
            warning: 0,
            error: 0,
            debug: 0
        };

        this.logBuffer.forEach(log => {
            if (stats.hasOwnProperty(log.type)) {
                stats[log.type]++;
            }
        });

        return stats;
    }

    // 清空特定类型的日志
    clearByType(type) {
        if (!this.container) return;

        const entries = this.container.querySelectorAll(`.log-${type}`);
        entries.forEach(entry => entry.remove());

        this.logBuffer = this.logBuffer.filter(log => log.type !== type);
        this.info(`已清空${this.getTypeLabel(type)}类型的日志`);
    }
}